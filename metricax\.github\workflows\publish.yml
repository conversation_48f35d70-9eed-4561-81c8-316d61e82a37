name: Publish to Py<PERSON>

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      test_pypi:
        description: 'Publish to Test PyPI instead of PyPI'
        required: false
        default: false
        type: boolean

jobs:
  publish:
    runs-on: ubuntu-latest
    environment: 
      name: ${{ github.event.inputs.test_pypi == 'true' && 'test-pypi' || 'pypi' }}
      url: ${{ github.event.inputs.test_pypi == 'true' && 'https://test.pypi.org/p/metricax' || 'https://pypi.org/p/metricax' }}
    
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing
      contents: read

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: python -m build

    - name: Check package
      run: twine check dist/*

    - name: Publish to Test PyPI
      if: github.event.inputs.test_pypi == 'true'
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/
        verbose: true

    - name: Publish to PyPI
      if: github.event.inputs.test_pypi != 'true'
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        verbose: true

  create-github-release:
    runs-on: ubuntu-latest
    needs: publish
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.test_pypi != 'true'
    
    permissions:
      contents: write

    steps:
    - uses: actions/checkout@v4

    - name: Extract version from pyproject.toml
      id: version
      run: |
        VERSION=$(python -c "import tomllib; print(tomllib.load(open('pyproject.toml', 'rb'))['project']['version'])")
        echo "version=$VERSION" >> $GITHUB_OUTPUT

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.version.outputs.version }}
        release_name: Release v${{ steps.version.outputs.version }}
        body: |
          ## What's Changed
          
          See [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md) for detailed changes.
          
          ## Installation
          
          ```bash
          pip install metricax==${{ steps.version.outputs.version }}
          ```
        draft: false
        prerelease: false
