{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MetricaX Information Theory Demo\n", "\n", "This notebook demonstrates the power of MetricaX's Information Theory module with interactive examples and visualizations.\n", "\n", "## Topics Covered\n", "1. **Entropy Analysis** - Measuring uncertainty and information content\n", "2. **Mutual Information** - Detecting dependencies between variables\n", "3. **Distribution Distances** - Comparing probability distributions\n", "4. **Coding Theory** - Optimal compression and communication\n", "5. **Real-World Applications** - Feature selection, model comparison, data analysis\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import metricax.info_theory as it\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import List, Dict\n", "import math\n", "\n", "# Set up plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "\n", "print(\"🔬 MetricaX Information Theory Demo\")\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Entropy Analysis 📊\n", "\n", "Entropy measures the uncertainty or information content in a probability distribution. Let's explore how entropy changes with different distribution shapes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create different types of distributions\n", "distributions = {\n", "    \"Uniform\": [0.25, 0.25, 0.25, 0.25],           # Maximum entropy\n", "    \"Slightly Skewed\": [0.4, 0.3, 0.2, 0.1],       # Moderate entropy\n", "    \"Highly Skewed\": [0.7, 0.2, 0.08, 0.02],       # Low entropy\n", "    \"Deterministic\": [1.0, 0.0, 0.0, 0.0],         # Zero entropy\n", "    \"Bimodal\": [0.45, 0.05, 0.05, 0.45],           # Interesting pattern\n", "}\n", "\n", "# Calculate entropy for each distribution\n", "entropies = {}\n", "for name, dist in distributions.items():\n", "    entropy = it.entropy(dist)\n", "    entropies[name] = entropy\n", "    print(f\"{name:15}: {dist} → Entropy: {entropy:.3f} bits\")\n", "\n", "# Visualize distributions and their entropies\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Plot distributions\n", "x_pos = np.arange(4)\n", "width = 0.15\n", "colors = sns.color_palette(\"husl\", len(distributions))\n", "\n", "for i, (name, dist) in enumerate(distributions.items()):\n", "    ax1.bar(x_pos + i*width, dist, width, label=name, color=colors[i], alpha=0.8)\n", "\n", "ax1.set_xlabel('Outcome')\n", "ax1.set_ylabel('Probability')\n", "ax1.set_title('Probability Distributions')\n", "ax1.set_xticks(x_pos + width*2)\n", "ax1.set_xticklabels(['A', 'B', 'C', 'D'])\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Plot entropies\n", "names = list(entropies.keys())\n", "entropy_values = list(entropies.values())\n", "bars = ax2.bar(names, entropy_values, color=colors, alpha=0.8)\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, entropy_values):\n", "    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,\n", "             f'{value:.3f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "ax2.set_ylabel('Entropy (bits)')\n", "ax2.set_title('Entropy Values')\n", "ax2.set_ylim(0, 2.2)\n", "ax2.tick_params(axis='x', rotation=45)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n💡 Key Insights:\")\n", "print(\"• Uniform distribution has maximum entropy (most uncertain)\")\n", "print(\"• Deterministic distribution has zero entropy (no uncertainty)\")\n", "print(\"• Entropy decreases as distribution becomes more concentrated\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}