# MetricaX Development Guide 🛠️

This guide covers the complete development workflow for MetricaX, including testing, code quality, and CI/CD processes.

## 🚀 Quick Setup

```bash
# Clone the repository
git clone https://github.com/mdshoaibuddinchanda/metricax.git
cd metricax

# Install in development mode with all dependencies
pip install -e ".[dev,examples,docs]"

# Install pre-commit hooks (recommended)
pre-commit install
```

## 🧪 Testing with Tox (Multi-Version Testing)

Tox is the recommended way to test across multiple Python versions locally:

### Basic Tox Commands

```bash
# Test all Python versions (3.8-3.12)
tox

# Test specific Python version
tox -e py311

# Run linting checks
tox -e lint

# Run type checking
tox -e type-check

# Run security checks
tox -e security

# Generate coverage report
tox -e coverage

# Build documentation
tox -e docs

# Build package
tox -e build

# Clean all build artifacts
tox -e clean
```

### Advanced Tox Usage

```bash
# Run tests with specific pytest arguments
tox -e py311 -- -v -k "test_beta"

# Run tests in parallel (faster)
tox -p auto

# Recreate environments (when dependencies change)
tox -r

# List all available environments
tox -l
```

## 🔍 Code Quality Tools

### Black (Code Formatting)

```bash
# Check formatting
black --check --diff metricax

# Auto-format code
black metricax
```

### isort (Import Sorting)

```bash
# Check import sorting
isort --check-only --diff metricax

# Auto-sort imports
isort metricax
```

### Flake8 (Linting)

```bash
# Run linting
flake8 metricax

# Check for critical errors only
flake8 metricax --select=E9,F63,F7,F82
```

### MyPy (Type Checking)

```bash
# Run type checking
mypy metricax

# Generate type coverage report
mypy metricax --html-report mypy-report
```

### Bandit (Security)

```bash
# Run security scan
bandit -r metricax

# Generate detailed report
bandit -r metricax -f json -o security-report.json
```

### Safety (Vulnerability Check)

```bash
# Check for known vulnerabilities
safety check

# Generate report
safety check --json --output safety-report.json
```

## 🧪 Testing Strategies

### Unit Testing

```bash
# Run all tests
pytest metricax/ -v

# Run with coverage
pytest metricax/ -v --cov=metricax --cov-report=html

# Run specific module tests
pytest metricax/bayesian/tests/ -v
pytest metricax/info_theory/tests/ -v

# Run performance benchmarks
pytest metricax/ -v --benchmark-only
```

### Test Categories

```bash
# Run only unit tests
pytest -m "unit"

# Run only integration tests
pytest -m "integration"

# Skip slow tests
pytest -m "not slow"
```

### Coverage Analysis

```bash
# Generate HTML coverage report
pytest --cov=metricax --cov-report=html
# Open htmlcov/index.html in browser

# Generate XML coverage (for CI)
pytest --cov=metricax --cov-report=xml

# Show missing lines
pytest --cov=metricax --cov-report=term-missing
```

## 🔄 Pre-commit Hooks

Pre-commit hooks run automatically before each commit to ensure code quality:

```bash
# Install hooks
pre-commit install

# Run hooks manually on all files
pre-commit run --all-files

# Run specific hook
pre-commit run black
pre-commit run mypy

# Update hook versions
pre-commit autoupdate
```

## 🏗️ CI/CD Workflows

### GitHub Actions Workflows

1. **`python-package.yml`** - Main CI/CD pipeline
   - Tests across Python 3.8-3.12 and OS (Ubuntu, Windows, macOS)
   - Code quality checks (black, isort, flake8, mypy, bandit)
   - Coverage reporting to Codecov
   - Tox testing matrix
   - Package building and validation

2. **`code-quality.yml`** - Dedicated code quality checks
   - Formatting, linting, type checking, security
   - Generates quality reports

3. **`publish.yml`** - PyPI publishing
   - Automatic publishing on releases
   - Manual publishing with test PyPI option
   - Trusted publishing with OIDC

### Workflow Badges

Add these badges to your README:

```markdown
![Tests](https://github.com/mdshoaibuddinchanda/metricax/actions/workflows/python-package.yml/badge.svg)
![Code Quality](https://github.com/mdshoaibuddinchanda/metricax/actions/workflows/code-quality.yml/badge.svg)
![Coverage](https://img.shields.io/codecov/c/github/mdshoaibuddinchanda/metricax)
```

## 📦 Package Management

### Building Packages

```bash
# Build source and wheel distributions
python -m build

# Check package integrity
twine check dist/*

# Upload to Test PyPI
twine upload --repository testpypi dist/*

# Upload to PyPI
twine upload dist/*
```

### Version Management

Update version in `pyproject.toml`:

```toml
[project]
version = "0.2.2"  # Update this
```

## 🐛 Debugging and Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you've installed in development mode: `pip install -e .`

2. **Test Failures**: Run tests with verbose output: `pytest -v -s`

3. **Type Errors**: Check MyPy configuration in `pyproject.toml`

4. **Coverage Issues**: Ensure all code paths are tested

### Debug Commands

```bash
# Verbose test output
pytest -v -s --tb=long

# Debug specific test
pytest metricax/bayesian/tests/test_beta.py::test_beta_pdf -v -s

# Profile test performance
pytest --benchmark-only --benchmark-sort=mean
```

## 📊 Performance Monitoring

### Benchmarking

```bash
# Run performance benchmarks
pytest --benchmark-only

# Compare benchmarks
pytest --benchmark-compare

# Save benchmark results
pytest --benchmark-save=baseline
```

### Memory Profiling

```bash
# Install memory profiler
pip install memory-profiler

# Profile memory usage
python -m memory_profiler your_script.py
```

## 🚀 Release Process

1. **Update Version**: Increment version in `pyproject.toml`
2. **Update Changelog**: Document changes in `CHANGELOG.md`
3. **Run Full Test Suite**: `tox`
4. **Build Package**: `python -m build`
5. **Test Package**: `twine check dist/*`
6. **Create Release**: Use GitHub releases or `gh release create`
7. **Publish**: Automatic via GitHub Actions or manual with `twine upload`

## 🤝 Contributing Workflow

1. **Fork Repository**: Create your own fork
2. **Create Branch**: `git checkout -b feature/your-feature`
3. **Install Dev Dependencies**: `pip install -e ".[dev]"`
4. **Install Pre-commit**: `pre-commit install`
5. **Make Changes**: Follow code style guidelines
6. **Run Tests**: `tox` or `pytest`
7. **Commit Changes**: Pre-commit hooks will run automatically
8. **Push Branch**: `git push origin feature/your-feature`
9. **Create PR**: Submit pull request for review

## 📚 Additional Resources

- **[Contributing Guide](CONTRIBUTING.md)** - Detailed contribution guidelines
- **[Technical Guide](TECHNICAL_GUIDE.md)** - Architecture and design decisions
- **[API Documentation](docs/)** - Complete API reference
- **[GitHub Actions Documentation](https://docs.github.com/en/actions)** - CI/CD reference
- **[Tox Documentation](https://tox.wiki/)** - Multi-environment testing
- **[Pre-commit Documentation](https://pre-commit.com/)** - Git hook framework

---

**Happy coding! 🎉**
