name: MetricaX Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest
        pip install -e .

    - name: Test basic imports
      run: |
        python -c "
        try:
            import metricax.bayesian as mb
            print('✅ Bayesian module imported successfully')
            result = mb.beta_mean(2, 3)
            print(f'✅ Bayesian function works: beta_mean(2,3) = {result}')
        except Exception as e:
            print(f'❌ Bayesian module failed: {e}')
            exit(1)

        try:
            import metricax.info_theory as it
            print('✅ Info theory module imported successfully')
            result = it.entropy([0.5, 0.5])
            print(f'✅ Info theory function works: entropy([0.5,0.5]) = {result}')
        except Exception as e:
            print(f'❌ Info theory module failed: {e}')
            exit(1)

        print('🎉 All basic functionality tests passed!')
        "

    - name: Run core tests
      run: |
        python -c "
        import sys
        import os

        # Add current directory to path
        sys.path.insert(0, '.')

        # Test core functionality
        try:
            import metricax.bayesian as mb
            import metricax.info_theory as it

            # Test a few key functions
            assert abs(mb.beta_mean(2, 3) - 0.4) < 1e-10
            assert abs(it.entropy([0.5, 0.5]) - 1.0) < 1e-10

            print('✅ Core functionality tests passed!')
        except Exception as e:
            print(f'❌ Core tests failed: {e}')
            sys.exit(1)
        "

  docs:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Check documentation files exist
      run: |
        ls -la docs/ || echo "docs directory not found"
        echo "Documentation check completed"

    - name: Validate documentation structure
      run: |
        python -c "
        import os
        required_files = ['README.md', 'pyproject.toml', 'CHANGELOG.md']
        for file in required_files:
            if os.path.exists(file):
                print(f'✅ {file} exists')
            else:
                print(f'❌ {file} missing')
        print('Documentation structure validated!')
        "
