# MetricaX Development Makefile

.PHONY: help install test test-all lint format type-check security clean build docs

# Default target
help:
	@echo "MetricaX Development Commands:"
	@echo ""
	@echo "Setup:"
	@echo "  install     Install package in development mode"
	@echo "  install-all Install with all optional dependencies"
	@echo ""
	@echo "Testing:"
	@echo "  test        Run tests with pytest"
	@echo "  test-all    Run tests with tox (all Python versions)"
	@echo "  coverage    Generate coverage report"
	@echo "  benchmark   Run performance benchmarks"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint        Run all linting checks"
	@echo "  format      Auto-format code with black and isort"
	@echo "  type-check  Run type checking with mypy"
	@echo "  security    Run security checks"
	@echo ""
	@echo "Build:"
	@echo "  build       Build package distributions"
	@echo "  docs        Build documentation"
	@echo "  clean       Clean build artifacts"
	@echo ""
	@echo "Git Hooks:"
	@echo "  pre-commit  Install pre-commit hooks"

# Setup commands
install:
	pip install -e ".[dev]"

install-all:
	pip install -e ".[dev,examples,docs,all]"

# Testing commands
test:
	pytest metricax/ -v --cov=metricax --cov-report=term-missing

test-all:
	tox

coverage:
	pytest metricax/ -v --cov=metricax --cov-report=html --cov-report=term-missing
	@echo "Coverage report generated in htmlcov/index.html"

benchmark:
	pytest metricax/ -v --benchmark-only

# Code quality commands
lint:
	flake8 metricax --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 metricax --count --exit-zero --max-complexity=10 --statistics

format:
	black metricax
	isort metricax

type-check:
	mypy metricax

security:
	bandit -r metricax
	safety check

# Build commands
build:
	python -m build
	twine check dist/*

docs:
	tox -e docs

clean:
	tox -e clean
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Git hooks
pre-commit:
	pre-commit install
	@echo "Pre-commit hooks installed"

# Quality check (run before committing)
check: format lint type-check security test
	@echo "All quality checks passed!"

# Full CI simulation
ci: clean install test-all build
	@echo "CI simulation completed successfully!"
