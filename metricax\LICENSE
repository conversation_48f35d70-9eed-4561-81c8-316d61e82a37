MIT License

Copyright (c) 2025 MetricaX Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

================================================================================

ADDITIONAL NOTICES FOR MATHEMATICAL COMPUTATIONS:

This software implements mathematical algorithms and statistical computations.
Users are responsible for:

1. Validating the appropriateness of algorithms for their specific use cases
2. Understanding the limitations and assumptions of implemented methods
3. Verifying computational results for critical applications
4. Ensuring compliance with applicable regulations in their domain

The contributors make no warranties regarding the mathematical accuracy,
numerical stability, or fitness for any particular scientific, financial,
or commercial purpose.

For questions about specific implementations or to report issues:
- GitHub Issues: https://github.com/mdshoaibuddinchanda/metricax/issues

================================================================================

THIRD-PARTY ACKNOWLEDGMENTS:

This software may implement algorithms described in academic papers and
textbooks. While the code is original, we acknowledge the mathematical
foundations provided by the broader scientific community.

Key mathematical references:
- Bayesian Statistics: Gelman et al., "Bayesian Data Analysis"
- Information Theory: Cover & Thomas, "Elements of Information Theory"
- Numerical Methods: Press et al., "Numerical Recipes"

================================================================================