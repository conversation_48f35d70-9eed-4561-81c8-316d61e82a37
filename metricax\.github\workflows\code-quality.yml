name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-quality-${{ hashFiles('**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-quality-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Check code formatting with black
      run: |
        black --check --diff metricax

    - name: Check import sorting with isort
      run: |
        isort --check-only --diff metricax

    - name: Lint with flake8
      run: |
        # Stop the build if there are Python syntax errors or undefined names
        flake8 metricax --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings
        flake8 metricax --count --exit-zero --max-complexity=10 --statistics

    - name: Type checking with mypy
      run: |
        mypy metricax

    - name: Security check with bandit
      run: |
        bandit -r metricax

    - name: Check for vulnerabilities with safety
      run: |
        safety check

    - name: Generate code quality report
      run: |
        echo "## Code Quality Report" >> $GITHUB_STEP_SUMMARY
        echo "### Formatting" >> $GITHUB_STEP_SUMMARY
        echo "✅ Black formatting check passed" >> $GITHUB_STEP_SUMMARY
        echo "✅ Import sorting check passed" >> $GITHUB_STEP_SUMMARY
        echo "### Linting" >> $GITHUB_STEP_SUMMARY
        echo "✅ Flake8 linting passed" >> $GITHUB_STEP_SUMMARY
        echo "### Type Checking" >> $GITHUB_STEP_SUMMARY
        echo "✅ MyPy type checking passed" >> $GITHUB_STEP_SUMMARY
        echo "### Security" >> $GITHUB_STEP_SUMMARY
        echo "✅ Bandit security scan passed" >> $GITHUB_STEP_SUMMARY
        echo "✅ Safety vulnerability check passed" >> $GITHUB_STEP_SUMMARY
