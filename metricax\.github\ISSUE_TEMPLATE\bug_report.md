---
name: Bug report
about: Create a report to help us improve MetricaX
title: '[BUG] '
labels: 'bug'
assignees: 'mds<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'

---

## Bug Description
A clear and concise description of what the bug is.

## To Reproduce
Steps to reproduce the behavior:
1. Import '...'
2. Call function '....'
3. With parameters '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
What actually happened, including any error messages.

## Code Example
```python
# Minimal code example that reproduces the issue
import metricax.bayesian as mb

# Your code here
```

## Error Output
```
Paste the full error traceback here
```

## Environment
- MetricaX version: [e.g., 0.2.1]
- Python version: [e.g., 3.11.0]
- Operating System: [e.g., Ubuntu 22.04, Windows 11, macOS 13]
- Installation method: [e.g., pip, conda, from source]

## Additional Context
Add any other context about the problem here, such as:
- Mathematical context or domain
- Expected mathematical behavior
- References to algorithms or papers
- Screenshots (if applicable)

## Checklist
- [ ] I have searched existing issues to ensure this is not a duplicate
- [ ] I have provided a minimal code example
- [ ] I have included the full error traceback
- [ ] I have specified my environment details
