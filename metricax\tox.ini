[tox]
envlist = py{38,39,310,311,312}, lint, type-check, security, coverage
isolated_build = True
skip_missing_interpreters = True

[testenv]
deps = 
    pytest>=7.0.0
    pytest-cov>=4.0.0
    pytest-benchmark>=4.0.0
commands = 
    pytest {posargs}

[testenv:lint]
deps = 
    black>=22.0.0
    flake8>=5.0.0
    isort>=5.0.0
commands = 
    black --check --diff metricax
    isort --check-only --diff metricax
    flake8 metricax

[testenv:type-check]
deps = 
    mypy>=1.0.0
commands = 
    mypy metricax

[testenv:security]
deps = 
    bandit>=1.7.0
    safety>=2.0.0
commands = 
    bandit -r metricax
    safety check

[testenv:coverage]
deps = 
    pytest>=7.0.0
    pytest-cov>=4.0.0
commands = 
    pytest --cov=metricax --cov-report=term-missing --cov-report=html --cov-report=xml

[testenv:docs]
deps = 
    sphinx>=5.0.0
    sphinx-rtd-theme>=1.0.0
    myst-parser>=0.18.0
commands = 
    sphinx-build -b html docs docs/_build/html

[testenv:build]
deps = 
    build
    twine
commands = 
    python -m build
    twine check dist/*

[testenv:clean]
deps = 
skip_install = true
commands = 
    python -c "import shutil; shutil.rmtree('dist', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('build', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('metricax.egg-info', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('.tox', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('htmlcov', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('.pytest_cache', ignore_errors=True)"
    python -c "import shutil; shutil.rmtree('.mypy_cache', ignore_errors=True)"

[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = 
    .git,
    __pycache__,
    docs/source/conf.py,
    old,
    build,
    dist,
    .eggs,
    *.egg,
    .tox

[coverage:run]
source = metricax
omit = 
    */tests/*,
    */examples/*,
    */notebooks/*,
    setup.py

[coverage:report]
exclude_lines = 
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
