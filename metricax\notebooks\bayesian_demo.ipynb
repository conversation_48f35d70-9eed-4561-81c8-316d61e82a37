{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MetricaX Bayesian Statistics Demo 🎯\n", "\n", "**Interactive walkthrough of Bayesian inference with real-world applications**\n", "\n", "This notebook demonstrates the power of MetricaX's Bayesian statistics module through practical examples and visualizations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install MetricaX if not already installed\n", "# !pip install metricax\n", "\n", "import metricax.bayesian as mb\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Set style for beautiful plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ MetricaX Bayesian module loaded successfully!\")\n", "print(f\"📊 Available functions: {len(mb.__all__)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 **A/B Testing with Bayesian Analysis**\n", "\n", "Let's analyze a real A/B test scenario where we're comparing conversion rates between two website variants."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# A/B Test Data\n", "control_visitors = 1000\n", "control_conversions = 85\n", "\n", "treatment_visitors = 1000  \n", "treatment_conversions = 110\n", "\n", "print(f\"🅰️ Control: {control_conversions}/{control_visitors} = {control_conversions/control_visitors:.1%}\")\n", "print(f\"🅱️ Treatment: {treatment_conversions}/{treatment_visitors} = {treatment_conversions/treatment_visitors:.1%}\")\n", "print(f\"📈 Observed lift: {(treatment_conversions/treatment_visitors - control_conversions/control_visitors)/(control_conversions/control_visitors):.1%}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bayesian Analysis with Beta-Binomial Conjugate Priors\n", "# Start with uninformative prior: Beta(1, 1)\n", "prior_alpha, prior_beta = 1, 1\n", "\n", "# Update with observed data\n", "control_alpha, control_beta = mb.update_beta_binomial(\n", "    prior_alpha, prior_beta, \n", "    control_conversions, control_visitors - control_conversions\n", ")\n", "\n", "treatment_alpha, treatment_beta = mb.update_beta_binomial(\n", "    prior_alpha, prior_beta,\n", "    treatment_conversions, treatment_visitors - treatment_conversions\n", ")\n", "\n", "print(f\"📊 Posterior Distributions:\")\n", "print(f\"   Control: Beta({control_alpha}, {control_beta})\")\n", "print(f\"   Treatment: Beta({treatment_alpha}, {treatment_beta})\")\n", "\n", "# Calculate posterior statistics\n", "control_mean = mb.beta_mean(control_alpha, control_beta)\n", "treatment_mean = mb.beta_mean(treatment_alpha, treatment_beta)\n", "control_var = mb.beta_var(control_alpha, control_beta)\n", "treatment_var = mb.beta_var(treatment_alpha, treatment_beta)\n", "\n", "print(f\"\\n📈 Posterior Estimates:\")\n", "print(f\"   Control: {control_mean:.1%} ± {1.96*np.sqrt(control_var):.1%}\")\n", "print(f\"   Treatment: {treatment_mean:.1%} ± {1.96*np.sqrt(treatment_var):.1%}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the posterior distributions\n", "x = np.linspace(0, 0.2, 1000)\n", "\n", "# Calculate PDFs\n", "control_pdf = [mb.beta_pdf(xi, control_alpha, control_beta) for xi in x]\n", "treatment_pdf = [mb.beta_pdf(xi, treatment_alpha, treatment_beta) for xi in x]\n", "\n", "# Create the plot\n", "plt.figure(figsize=(12, 8))\n", "\n", "plt.subplot(2, 2, 1)\n", "plt.plot(x, control_pdf, label='Control', linewidth=3, alpha=0.8)\n", "plt.plot(x, treatment_pdf, label='Treatment', linewidth=3, alpha=0.8)\n", "plt.axvline(control_mean, color='blue', linestyle='--', alpha=0.7, label=f'Control Mean: {control_mean:.1%}')\n", "plt.axvline(treatment_mean, color='orange', linestyle='--', alpha=0.7, label=f'Treatment Mean: {treatment_mean:.1%}')\n", "plt.xlabel('Conversion Rate')\n", "plt.ylabel('Probability Density')\n", "plt.title('Posterior Distributions of Conversion Rates')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Monte Carlo simulation for probability that treatment > control\n", "n_samples = 100000\n", "control_samples = np.random.beta(control_alpha, control_beta, n_samples)\n", "treatment_samples = np.random.beta(treatment_alpha, treatment_beta, n_samples)\n", "\n", "prob_treatment_better = np.mean(treatment_samples > control_samples)\n", "lift_samples = (treatment_samples - control_samples) / control_samples\n", "\n", "plt.subplot(2, 2, 2)\n", "plt.hist(lift_samples, bins=50, alpha=0.7, density=True, color='green')\n", "plt.axvline(np.mean(lift_samples), color='red', linestyle='--', linewidth=2, label=f'Mean Lift: {np.mean(lift_samples):.1%}')\n", "plt.xlabel('Relative Lift')\n", "plt.ylabel('Density')\n", "plt.title('Distribution of Relative Lift')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(2, 2, 3)\n", "credible_intervals = [50, 80, 95, 99]\n", "lift_percentiles = []\n", "for ci in credible_intervals:\n", "    lower = (100 - ci) / 2\n", "    upper = 100 - lower\n", "    lift_lower = np.percentile(lift_samples, lower)\n", "    lift_upper = np.percentile(lift_samples, upper)\n", "    lift_percentiles.append((lift_lower, lift_upper))\n", "    plt.barh(ci, lift_upper - lift_lower, left=lift_lower, alpha=0.6, label=f'{ci}% CI')\n", "\n", "plt.xlabel('Relative Lift')\n", "plt.ylabel('Credible Interval (%)')\n", "plt.title('Credible Intervals for Lift')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(2, 2, 4)\n", "decision_metrics = {\n", "    'P(Treatment > Control)': prob_treatment_better,\n", "    'Expected Lift': np.mean(lift_samples),\n", "    '95% CI Lower': np.percentile(lift_samples, 2.5),\n", "    '95% CI Upper': np.percentile(lift_samples, 97.5)\n", "}\n", "\n", "metrics_text = \"\\n\".join([f\"{k}: {v:.1%}\" if 'P(' in k or 'Lift' in k or 'CI' in k else f\"{k}: {v:.3f}\" \n", "                         for k, v in decision_metrics.items()])\n", "plt.text(0.1, 0.5, metrics_text, fontsize=12, verticalalignment='center', \n", "         bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightblue\", alpha=0.8))\n", "plt.xlim(0, 1)\n", "plt.ylim(0, 1)\n", "plt.axis('off')\n", "plt.title('Decision Metrics')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n🎯 **Decision Analysis:**\")\n", "print(f\"   Probability Treatment > Control: {prob_treatment_better:.1%}\")\n", "print(f\"   Expected Relative Lift: {np.mean(lift_samples):.1%}\")\n", "print(f\"   95% Credible Interval: [{np.percentile(lift_samples, 2.5):.1%}, {np.percentile(lift_samples, 97.5):.1%}]\")\n", "\n", "if prob_treatment_better > 0.95:\n", "    print(\"\\n✅ **Recommendation: Implement Treatment** (High confidence)\")\n", "elif prob_treatment_better > 0.8:\n", "    print(\"\\n⚠️ **Recommendation: Likely implement Treatment** (Moderate confidence)\")\n", "else:\n", "    print(\"\\n🤔 **Recommendation: Continue testing** (Insufficient evidence)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 **Online Learning: Quality Control**\n", "\n", "Demonstrate how Bayesian methods excel at online learning scenarios where beliefs are updated as new data arrives."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simulate a quality control scenario\n", "# Initial belief: defect rate around 2%\n", "alpha, beta = 2, 98\n", "batches = [(100, 3), (150, 2), (200, 5), (120, 1), (180, 4), (90, 6), (110, 2)]\n", "\n", "# Track evolution of beliefs\n", "evolution = []\n", "cumulative_items = 0\n", "cumulative_defects = 0\n", "\n", "for i, (items, defects) in enumerate(batches):\n", "    cumulative_items += items\n", "    cumulative_defects += defects\n", "    \n", "    # Update beliefs\n", "    alpha, beta = mb.update_beta_binomial(alpha, beta, defects, items - defects)\n", "    \n", "    current_rate = mb.beta_mean(alpha, beta)\n", "    current_var = mb.beta_var(alpha, beta)\n", "    \n", "    evolution.append({\n", "        'batch': i + 1,\n", "        'items': items,\n", "        'defects': defects,\n", "        'cumulative_items': cumulative_items,\n", "        'cumulative_defects': cumulative_defects,\n", "        'alpha': alpha,\n", "        'beta': beta,\n", "        'estimated_rate': current_rate,\n", "        'uncertainty': np.sqrt(current_var),\n", "        'observed_rate': cumulative_defects / cumulative_items\n", "    })\n", "\n", "# Visualize the evolution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Plot 1: Evolution of rate estimate\n", "batches_num = [e['batch'] for e in evolution]\n", "estimated_rates = [e['estimated_rate'] for e in evolution]\n", "uncertainties = [e['uncertainty'] for e in evolution]\n", "observed_rates = [e['observed_rate'] for e in evolution]\n", "\n", "axes[0, 0].plot(batches_num, estimated_rates, 'o-', label='Bayesian Estimate', linewidth=2, markersize=8)\n", "axes[0, 0].fill_between(batches_num, \n", "                       [r - 1.96*u for r, u in zip(estimated_rates, uncertainties)],\n", "                       [r + 1.96*u for r, u in zip(estimated_rates, uncertainties)],\n", "                       alpha=0.3, label='95% Credible Interval')\n", "axes[0, 0].plot(batches_num, observed_rates, 's--', label='Observed Rate', alpha=0.7)\n", "axes[0, 0].axhline(0.03, color='red', linestyle=':', label='<PERSON><PERSON> (3%)')\n", "axes[0, 0].set_xlabel('Batch Number')\n", "axes[0, 0].set_ylabel('Defect Rate')\n", "axes[0, 0].set_title('Evolution of Defect Rate Estimates')\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Uncertainty reduction\n", "axes[0, 1].plot(batches_num, uncertainties, 'ro-', linewidth=2, markersize=8)\n", "axes[0, 1].set_xlabel('Batch Number')\n", "axes[0, 1].set_ylabel('Uncertainty (Standard Deviation)')\n", "axes[0, 1].set_title('Uncertainty Reduction Over Time')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Posterior distributions evolution\n", "x_range = np.linspace(0, 0.08, 1000)\n", "colors = plt.cm.viridis(np.linspace(0, 1, len(evolution)))\n", "\n", "for i, (evo, color) in enumerate(zip(evolution, colors)):\n", "    if i % 2 == 0:  # Show every other batch to avoid clutter\n", "        pdf_vals = [mb.beta_pdf(x, evo['alpha'], evo['beta']) for x in x_range]\n", "        axes[1, 0].plot(x_range, pdf_vals, color=color, alpha=0.8, \n", "                       label=f\"Batch {evo['batch']}\")\n", "\n", "axes[1, 0].set_xlabel('Defect Rate')\n", "axes[1, 0].set_ylabel('Probability Density')\n", "axes[1, 0].set_title('Evolution of Posterior Distributions')\n", "axes[1, 0].legend()\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 4: Summary statistics\n", "final_stats = evolution[-1]\n", "stats_text = f\"\"\"Final Results (After {final_stats['cumulative_items']} items):\n", "\n", "Estimated Defect Rate: {final_stats['estimated_rate']:.2%}\n", "95% Credible Interval: [{final_stats['estimated_rate'] - 1.96*final_stats['uncertainty']:.2%}, \n", "{final_stats['estimated_rate'] + 1.96*final_stats['uncertainty']:.2%}]\n", "\n", "Observed Defect Rate: {final_stats['observed_rate']:.2%}\n", "Total Defects: {final_stats['cumulative_defects']}\n", "\n", "Posterior: Beta({final_stats['alpha']:.0f}, {final_stats['beta']:.0f})\"\"\"\n", "\n", "axes[1, 1].text(0.05, 0.5, stats_text, fontsize=11, verticalalignment='center',\n", "                bbox=dict(boxstyle=\"round,pad=0.5\", facecolor=\"lightgreen\", alpha=0.8))\n", "axes[1, 1].set_xlim(0, 1)\n", "axes[1, 1].set_ylim(0, 1)\n", "axes[1, 1].axis('off')\n", "axes[1, 1].set_title('Final Summary')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n🏭 **Quality Control Analysis Complete!**\")\n", "print(f\"   Final defect rate estimate: {final_stats['estimated_rate']:.2%}\")\n", "print(f\"   Uncertainty: ±{1.96*final_stats['uncertainty']:.2%} (95% CI)\")\n", "if final_stats['estimated_rate'] > 0.03:\n", "    print(\"   🚨 **ALERT**: Defect rate exceeds 3% threshold!\")\n", "else:\n", "    print(\"   ✅ **OK**: Defect rate within acceptable limits\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎓 **Key Takeaways**\n", "\n", "This demo showcased the power of MetricaX's Bayesian statistics module:\n", "\n", "1. **🎯 A/B Testing**: Rigorous statistical analysis with uncertainty quantification\n", "2. **🔄 Online Learning**: Beliefs update naturally as new data arrives\n", "3. **📊 Visualization**: Clear communication of statistical results\n", "4. **💼 Business Impact**: Actionable insights for decision-making\n", "\n", "### **Next Steps:**\n", "- Explore the Information Theory demo notebook\n", "- Check out the examples in `metricax/bayesian/examples/`\n", "- Read the comprehensive documentation\n", "- Try MetricaX on your own data!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}