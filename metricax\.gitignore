# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Virtual environment
venv/
env/
.venv/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
.coverage
.tox/
.nox/
.cache/
pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# pytype
.pytype/

# Cython debug symbols
cython_debug/

# VSCode
.vscode/

# System files
.DS_Store
Thumbs.db

# PyProject local metadata (optional)
__pypackages__/
