# Pull Request

## Description
Brief description of the changes in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Mathematical Changes
If this PR involves mathematical functions:
- **Domain**: [e.g., Bayesian Statistics, Information Theory]
- **Functions Added/Modified**: 
- **Mathematical Validation**: [How was correctness verified?]
- **Numerical Stability**: [Any special considerations?]

## Testing
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have added tests for edge cases and error conditions
- [ ] I have run the full test suite with `tox`

## Code Quality
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have run `make check` and all quality checks pass

## Documentation
- [ ] I have updated docstrings with proper mathematical notation
- [ ] I have added examples to demonstrate usage
- [ ] I have updated README.md if needed
- [ ] I have added references to mathematical sources

## Performance
- [ ] I have considered the performance impact of my changes
- [ ] I have added benchmarks for new functions (if applicable)
- [ ] Memory usage is reasonable for the functionality provided

## Breaking Changes
If this is a breaking change, describe what breaks and how users should adapt:

## Checklist
- [ ] I have read the [Contributing Guidelines](CONTRIBUTING.md)
- [ ] I have read the [Development Guide](DEVELOPMENT.md)
- [ ] My branch is up to date with the main branch
- [ ] I have squashed my commits into logical units
- [ ] I have written clear commit messages

## Additional Notes
Any additional information that reviewers should know.
